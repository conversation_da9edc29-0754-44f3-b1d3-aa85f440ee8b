APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# SEO Configuration
SEO_TOOLS_INERTIA=true
SEO_DEFAULT_TITLE="MbokaTour - Redécouvre Kinshasa autrement"
SEO_DEFAULT_DESCRIPTION="L'application qui te fait redécouvrir Kinshasa autrement. Découvre les meilleurs endroits à visiter, les événements culturels et les bons plans près de chez toi."
SEO_DEFAULT_KEYWORDS="Kinshasa,tourisme,Congo,RDC,voyage,culture,événements,lieux,découverte,guide,application mobile,bons plans"
SEO_TWITTER_SITE="@MbokaTour"
SEO_OG_IMAGE="/images/og-image.jpg"

# App Version Configuration
ANDROID_MINIMUM_VERSION=1.0.0
ANDROID_LATEST_VERSION=1.1.0
ANDROID_STORE_URL=https://play.google.com/store/apps/details?id=com.mbokatour.app

IOS_MINIMUM_VERSION=1.0.0
IOS_LATEST_VERSION=1.1.0
IOS_STORE_URL=https://apps.apple.com/app/mbokatour/id123456789
