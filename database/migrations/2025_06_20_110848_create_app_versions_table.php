<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_versions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->enum('platform', ['android', 'ios']);
            $table->string('version');
            $table->text('description')->nullable();
            $table->string('file_path')->nullable(); // Chemin vers le fichier APK/IPA
            $table->string('download_url')->nullable(); // URL de téléchargement externe (Play Store, App Store)
            $table->bigInteger('file_size')->nullable(); // Taille du fichier en bytes
            $table->boolean('is_active')->default(true);
            $table->boolean('force_update')->default(false); // Pour forcer la mise à jour
            $table->json('release_notes')->nullable(); // Notes de version
            $table->timestamp('released_at')->nullable();
            $table->timestamps();

            // Index pour optimiser les requêtes
            $table->index(['platform', 'is_active']);
            $table->index(['platform', 'version']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_versions');
    }
};
