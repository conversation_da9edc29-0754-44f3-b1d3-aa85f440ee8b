<?php

use App\Http\Controllers\Admin\AppVersionController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\EventController;
use App\Http\Controllers\Admin\PlaceController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DownloadController;
use App\Http\Controllers\SitemapController;
use App\Http\Controllers\WelcomeController;
use Illuminate\Support\Facades\Route;

Route::get('/', [WelcomeController::class, 'index'])->name('home');

// Download Routes
Route::get('/download', [DownloadController::class, 'index'])->name('download.index');
Route::get('/download/{platform}', [DownloadController::class, 'download'])->name('download.platform');
Route::get('/download/{platform}/qr', [DownloadController::class, 'qrCode'])->name('download.qr');
Route::get('/download/{platform}/qr/download', [DownloadController::class, 'downloadQrCode'])->name('download.qr.download');

// SEO Routes
Route::get('/sitemap.xml', [SitemapController::class, 'index'])->name('sitemap');

Route::get('dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Routes pour les places avec permission spécifique
    Route::middleware(['permission:manage-places'])->group(function () {
        Route::resource('places', PlaceController::class);
        // Route POST pour la mise à jour des places avec images
        Route::post('places/{place}/update-with-images', [PlaceController::class, 'updateWithImages'])->name('places.update-with-images');
        // Route pour supprimer une image de place
        Route::delete('places/{place}/images/{image}', [PlaceController::class, 'deleteImage'])->name('places.images.delete');
    });

    // Routes pour les événements avec permission spécifique
    Route::middleware(['permission:manage-events'])->group(function () {
        Route::resource('events', EventController::class);
    });

    // Routes pour les catégories avec permission spécifique
    Route::middleware(['permission:manage-categories'])->group(function () {
        Route::resource('categories', CategoryController::class);
    });

    // Routes pour les utilisateurs avec permission spécifique
    Route::middleware(['permission:manage-users'])->group(function () {
        Route::resource('users', UserController::class);
    });

    // Routes pour les versions d'applications avec permission spécifique
    Route::middleware(['permission:manage-app-versions'])->group(function () {
        Route::resource('app-versions', AppVersionController::class);
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
