<?php

use App\Http\Controllers\Api\AppVersionController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\CommentController;
use App\Http\Controllers\Api\PlaceController;
use App\Http\Controllers\Api\PlaceLikeController;
use App\Http\Controllers\Api\UserFavoritePlaceController;
use Illuminate\Support\Facades\Route;

// Routes d'authentification (publiques)
Route::post('/auth/register', [AuthController::class, 'register']);
Route::post('/auth/login', [AuthController::class, 'login']);

// Routes publiques
Route::get('/app/version', [AppVersionController::class, 'checkVersion']);
Route::get('/categories', [CategoryController::class, 'index']);
Route::get('/places/discover', [PlaceController::class, 'discover']);
Route::get('/places/nearby', [PlaceController::class, 'nearby']);
Route::get('/places/{id}', [PlaceController::class, 'show']);
Route::get('/places/{id}/comments', [CommentController::class, 'index']);
Route::get('/search', [PlaceController::class, 'search']);

// Routes protégées (nécessitent une authentification)
Route::middleware('auth:sanctum')->group(function () {
    // Authentification
    Route::get('/auth/user', [AuthController::class, 'user']);
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::post('/auth/logout-all', [AuthController::class, 'logoutAll']);

    // Favoris
    Route::get('/user/favorites/places', [UserFavoritePlaceController::class, 'index']);
    Route::post('/user/favorites/places', [UserFavoritePlaceController::class, 'store']);
    Route::delete('/user/favorites/places/{place_id}', [UserFavoritePlaceController::class, 'destroy']);

    // Likes
    Route::post('/places/likes/toggle', [PlaceLikeController::class, 'toggle']);
    Route::get('/places/{place_id}/likes/status', [PlaceLikeController::class, 'status']);
    Route::get('/places/{place_id}/likes/stats', [PlaceLikeController::class, 'stats']);
    Route::get('/user/likes/places', [PlaceLikeController::class, 'index']);

    // Commentaires
    Route::post('/places/{id}/comments', [CommentController::class, 'store']);
    Route::put('/places/{place_id}/comments/{comment_id}', [CommentController::class, 'update']);
    Route::delete('/places/{place_id}/comments/{comment_id}', [CommentController::class, 'destroy']);
});
