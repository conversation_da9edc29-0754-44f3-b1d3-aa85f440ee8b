# API de Vérification de Version - MbokaTour

## Vue d'ensemble
Documentation de l'endpoint API pour la vérification de version et les mises à jour forcées de l'application MbokaTour.

## Endpoint

### GET /api/app/version

Vérifie la version de l'application et retourne les informations de mise à jour.

#### Paramètres de requête

| Paramètre | Type | Obligatoire | Description |
|-----------|------|-------------|-------------|
| `current_version` | string | Oui | Version actuelle de l'app (format: "x.y.z") |
| `platform` | string | Oui | Plateforme ("android", "ios") |

#### Exemple de requête

```http
GET /api/app/version?current_version=1.0.0&platform=android
```

#### Réponse

```json
{
  "minimum_version": "1.0.0",
  "latest_version": "1.1.0",
  "force_update": false,
  "has_update": true,
  "update_message": "Une nouvelle version de MbokaTour est disponible avec de nouvelles fonctionnalités !",
  "download_url": "https://play.google.com/store/apps/details?id=com.mbokatour.app",
  "features": [
    "Nouvelles destinations à découvrir",
    "Interface utilisateur améliorée",
    "Nouvelles fonctionnalités de recherche",
    "Optimisations de performance",
    "Corrections de bugs mineurs"
  ]
}
```

#### Structure de la réponse

| Champ | Type | Description |
|-------|------|-------------|
| `minimum_version` | string | Version minimale requise pour utiliser l'app |
| `latest_version` | string | Dernière version disponible |
| `force_update` | boolean | Si true, l'utilisateur DOIT mettre à jour |
| `has_update` | boolean | Si true, une mise à jour est disponible |
| `update_message` | string\|null | Message à afficher à l'utilisateur |
| `download_url` | string | URL vers le store (Play Store/App Store) |
| `features` | array\|null | Liste des nouvelles fonctionnalités |

## Logique de mise à jour

### Mise à jour forcée (`force_update: true`)
- L'utilisateur ne peut pas utiliser l'app
- Le dialogue ne peut pas être fermé
- Pas de bouton "Plus tard"
- Redirection obligatoire vers le store

### Mise à jour optionnelle (`has_update: true`, `force_update: false`)
- L'utilisateur peut choisir
- Bouton "Plus tard" disponible
- L'app continue de fonctionner

### Aucune mise à jour
- L'app continue normalement
- Aucun dialogue affiché

## Configuration

### Variables d'environnement

Ajoutez ces variables dans votre fichier `.env` :

```env
# Android
ANDROID_MINIMUM_VERSION=1.0.0
ANDROID_LATEST_VERSION=1.1.0
ANDROID_STORE_URL=https://play.google.com/store/apps/details?id=com.mbokatour.app

# iOS
IOS_MINIMUM_VERSION=1.0.0
IOS_LATEST_VERSION=1.1.0
IOS_STORE_URL=https://apps.apple.com/app/mbokatour/id123456789
```

## Codes d'erreur

### 422 - Validation Error

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "current_version": [
      "The current version field is required."
    ],
    "platform": [
      "The selected platform is invalid."
    ]
  }
}
```

## Exemples d'utilisation

### cURL

```bash
# Test mise à jour disponible
curl "http://localhost:8000/api/app/version?current_version=1.0.0&platform=android"

# Test mise à jour forcée
curl "http://localhost:8000/api/app/version?current_version=0.9.0&platform=android"

# Test aucune mise à jour
curl "http://localhost:8000/api/app/version?current_version=1.1.0&platform=android"
```

### JavaScript/Fetch

```javascript
async function checkAppVersion(currentVersion, platform) {
  try {
    const response = await fetch(
      `https://api.mbokatour.com/api/app/version?current_version=${currentVersion}&platform=${platform}`
    );
    
    if (!response.ok) {
      throw new Error('Erreur lors de la vérification de version');
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Erreur:', error);
    throw error;
  }
}

// Utilisation
checkAppVersion('1.0.0', 'android')
  .then(versionInfo => {
    if (versionInfo.force_update) {
      // Afficher dialogue de mise à jour forcée
      showForceUpdateDialog(versionInfo);
    } else if (versionInfo.has_update) {
      // Afficher dialogue de mise à jour optionnelle
      showOptionalUpdateDialog(versionInfo);
    }
  });
```

### Flutter/Dart

```dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class VersionCheckService {
  static const String baseUrl = 'https://api.mbokatour.com';
  
  static Future<Map<String, dynamic>> checkVersion(
    String currentVersion, 
    String platform
  ) async {
    final uri = Uri.parse('$baseUrl/api/app/version')
        .replace(queryParameters: {
      'current_version': currentVersion,
      'platform': platform,
    });
    
    final response = await http.get(uri);
    
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Erreur lors de la vérification de version');
    }
  }
}

// Utilisation
try {
  final versionInfo = await VersionCheckService.checkVersion('1.0.0', 'android');
  
  if (versionInfo['force_update']) {
    // Afficher dialogue de mise à jour forcée
    _showForceUpdateDialog(versionInfo);
  } else if (versionInfo['has_update']) {
    // Afficher dialogue de mise à jour optionnelle
    _showOptionalUpdateDialog(versionInfo);
  }
} catch (e) {
  print('Erreur: $e');
}
```

## Notes importantes

1. **Sécurité** : Cet endpoint est public (pas d'authentification requise)
2. **Cache** : Considérez mettre en cache la réponse côté client
3. **Monitoring** : Surveillez les appels pour détecter les versions obsolètes
4. **Rollback** : Gardez la possibilité de revenir en arrière en cas de problème
5. **Tests** : Utilisez `php artisan test tests/Feature/Api/AppVersionTest.php` pour tester l'implémentation
