# Système de Téléchargement d'Applications MbokaTour

Ce document décrit le système de téléchargement d'applications mis en place pour MbokaTour, permettant aux utilisateurs de télécharger l'application mobile et aux administrateurs de gérer les versions.

## Vue d'ensemble

Le système comprend :
- **Page de téléchargement publique** : `/download`
- **Interface d'administration** : `/admin/app-versions`
- **API de téléchargement** : `/download/{platform}`
- **Détection automatique de plateforme**
- **Gestion des fichiers APK/IPA**
- **Support des URLs externes (Play Store, App Store)**

## Fonctionnalités

### Pour les utilisateurs
- **Détection automatique de plateforme** : Le système détecte si l'utilisateur est sur Android ou iOS
- **Page de téléchargement dédiée** : Instructions claires selon la plateforme
- **Redirection intelligente** : Vers les stores officiels ou téléchargement direct
- **Interface responsive** : Optimisée pour mobile et desktop

### Pour les administrateurs
- **Gestion complète des versions** : <PERSON><PERSON><PERSON>, modifier, supprimer des versions
- **Upload de fichiers** : Support APK (Android) et IPA (iOS)
- **URLs externes** : Liens vers Play Store et App Store
- **Versioning** : Gestion des numéros de version et notes de release
- **Activation/Désactivation** : Contrôle des versions actives
- **Mises à jour forcées** : Possibilité de forcer les utilisateurs à mettre à jour

## Structure des données

### Modèle AppVersion

```php
- id (UUID)
- platform (enum: 'android', 'ios')
- version (string)
- description (text, nullable)
- file_path (string, nullable) // Chemin vers APK/IPA
- download_url (string, nullable) // URL externe
- file_size (bigint, nullable)
- is_active (boolean)
- force_update (boolean)
- release_notes (json, nullable)
- released_at (timestamp, nullable)
- created_at, updated_at
```

## Utilisation

### 1. Accès utilisateur

Les boutons de téléchargement sur la page d'accueil redirigent vers :
- `/download?platform=android` pour Android
- `/download?platform=ios` pour iOS
- `/download` pour détection automatique

### 2. Administration

**Accès** : `/admin/app-versions` (nécessite permission `manage-app-versions`)

**Actions disponibles** :
- **Créer une version** : Upload de fichier ou URL externe
- **Modifier une version** : Mise à jour des informations
- **Activer/Désactiver** : Contrôle de la visibilité
- **Supprimer** : Suppression complète (fichier inclus)

### 3. Workflow recommandé

1. **Développement** : Créer l'APK/IPA
2. **Upload** : Utiliser l'interface admin pour uploader
3. **Configuration** : Définir version, notes de release, etc.
4. **Test** : Tester le téléchargement via la page publique
5. **Activation** : Activer la version pour la rendre publique
6. **Publication** : Optionnel - publier sur les stores et mettre à jour l'URL

## Configuration

### Permissions

Ajouter la permission `manage-app-versions` aux rôles administrateurs :

```php
// Dans RolePermissionSeeder
$permissions = [
    'manage-places',
    'manage-events', 
    'manage-categories',
    'manage-users',
    'manage-app-versions', // Nouvelle permission
];
```

### Stockage

Les fichiers sont stockés dans `storage/app/public/app-versions/`

Assurer que le lien symbolique est créé :
```bash
php artisan storage:link
```

### Validation

- **Android** : Seuls les fichiers `.apk` sont acceptés
- **iOS** : Seuls les fichiers `.ipa` sont acceptés  
- **Taille max** : 100MB par fichier
- **Versions** : Une seule version active par plateforme

## API

### GET /download
Affiche la page de téléchargement avec détection de plateforme

**Paramètres** :
- `platform` (optionnel) : 'android' ou 'ios'

### GET /download/{platform}
Télécharge ou redirige vers la dernière version active

**Paramètres** :
- `platform` : 'android' ou 'ios'

**Comportement** :
- Si `download_url` existe → Redirection
- Si `file_path` existe → Téléchargement direct
- Sinon → Erreur avec redirection vers `/download`

## Sécurité

- **Authentification** : Interface admin protégée par middleware `auth` et `admin`
- **Permissions** : Contrôle d'accès via Spatie Permission
- **Validation** : Validation stricte des types de fichiers
- **Stockage sécurisé** : Fichiers stockés hors du webroot public

## Maintenance

### Nettoyage des anciens fichiers

```php
// Script de nettoyage (à exécuter périodiquement)
$oldVersions = AppVersion::where('is_active', false)
    ->where('created_at', '<', now()->subMonths(6))
    ->get();

foreach ($oldVersions as $version) {
    if ($version->file_path && Storage::disk('public')->exists($version->file_path)) {
        Storage::disk('public')->delete($version->file_path);
    }
    $version->delete();
}
```

### Monitoring

- Surveiller l'espace disque utilisé par `/storage/app/public/app-versions/`
- Vérifier régulièrement que les URLs externes sont toujours valides
- Monitorer les téléchargements via les logs Laravel

## Dépannage

### Problèmes courants

1. **Fichier non trouvé** : Vérifier que `storage:link` est créé
2. **Permission denied** : Vérifier les permissions du dossier storage
3. **Upload échoue** : Vérifier `upload_max_filesize` et `post_max_size` dans php.ini
4. **Page 404** : Vérifier que les routes sont bien enregistrées

### Logs

Les erreurs sont loggées dans `storage/logs/laravel.log`

### Debug

```php
// Vérifier les versions actives
AppVersion::active()->get();

// Tester la récupération de version
AppVersion::getLatestVersion('android');
```

## Évolutions futures

- **Statistiques de téléchargement** : Tracking des downloads
- **Notifications push** : Alertes pour nouvelles versions
- **Rollback automatique** : En cas de problème avec une version
- **Tests A/B** : Déploiement progressif des versions
- **API mobile** : Endpoint pour vérification de version depuis l'app
