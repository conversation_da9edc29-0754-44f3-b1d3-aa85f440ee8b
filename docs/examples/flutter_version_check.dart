import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// Service pour gérer la vérification de version de l'application
class VersionCheckService {
  static const String baseUrl = 'https://api.mbokatour.com';
  
  /// Vérifie la version de l'application
  static Future<VersionInfo> checkVersion() async {
    try {
      // Obtenir les informations de l'application
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;
      final platform = Theme.of(navigatorKey.currentContext!).platform == TargetPlatform.iOS ? 'ios' : 'android';
      
      final uri = Uri.parse('$baseUrl/api/app/version').replace(queryParameters: {
        'current_version': currentVersion,
        'platform': platform,
      });
      
      final response = await http.get(
        uri,
        headers: {'Accept': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return VersionInfo.fromJson(data);
      } else {
        throw Exception('Erreur lors de la vérification de version: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erreur de connexion: $e');
    }
  }
}

/// Modèle de données pour les informations de version
class VersionInfo {
  final String minimumVersion;
  final String latestVersion;
  final bool forceUpdate;
  final bool hasUpdate;
  final String? updateMessage;
  final String downloadUrl;
  final List<String>? features;

  VersionInfo({
    required this.minimumVersion,
    required this.latestVersion,
    required this.forceUpdate,
    required this.hasUpdate,
    this.updateMessage,
    required this.downloadUrl,
    this.features,
  });

  factory VersionInfo.fromJson(Map<String, dynamic> json) {
    return VersionInfo(
      minimumVersion: json['minimum_version'],
      latestVersion: json['latest_version'],
      forceUpdate: json['force_update'],
      hasUpdate: json['has_update'],
      updateMessage: json['update_message'],
      downloadUrl: json['download_url'],
      features: json['features']?.cast<String>(),
    );
  }
}

/// Widget pour afficher le dialogue de mise à jour
class UpdateDialog extends StatelessWidget {
  final VersionInfo versionInfo;

  const UpdateDialog({Key? key, required this.versionInfo}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        versionInfo.forceUpdate ? 'Mise à jour obligatoire' : 'Mise à jour disponible',
        style: TextStyle(
          color: versionInfo.forceUpdate ? Colors.red : Colors.blue,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (versionInfo.updateMessage != null)
            Text(versionInfo.updateMessage!),
          const SizedBox(height: 16),
          if (versionInfo.features != null && versionInfo.features!.isNotEmpty) ...[
            const Text(
              'Nouvelles fonctionnalités :',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...versionInfo.features!.map(
              (feature) => Padding(
                padding: const EdgeInsets.only(left: 8, bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• '),
                    Expanded(child: Text(feature)),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
      actions: [
        if (!versionInfo.forceUpdate)
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Plus tard'),
          ),
        ElevatedButton(
          onPressed: () => _launchStore(versionInfo.downloadUrl),
          style: ElevatedButton.styleFrom(
            backgroundColor: versionInfo.forceUpdate ? Colors.red : Colors.blue,
          ),
          child: Text(
            versionInfo.forceUpdate ? 'Mettre à jour maintenant' : 'Mettre à jour',
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ],
    );
  }

  void _launchStore(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}

/// Mixin pour gérer la vérification de version dans l'application
mixin VersionCheckMixin<T extends StatefulWidget> on State<T> {
  
  /// Vérifie la version au démarrage de l'application
  void checkVersionOnStartup() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performVersionCheck();
    });
  }

  /// Effectue la vérification de version
  Future<void> _performVersionCheck() async {
    try {
      final versionInfo = await VersionCheckService.checkVersion();
      
      if (versionInfo.forceUpdate || versionInfo.hasUpdate) {
        _showUpdateDialog(versionInfo);
      }
    } catch (e) {
      // Log l'erreur mais ne pas bloquer l'application
      debugPrint('Erreur lors de la vérification de version: $e');
    }
  }

  /// Affiche le dialogue de mise à jour
  void _showUpdateDialog(VersionInfo versionInfo) {
    showDialog(
      context: context,
      barrierDismissible: !versionInfo.forceUpdate,
      builder: (context) => UpdateDialog(versionInfo: versionInfo),
    );
  }
}

/// Exemple d'utilisation dans l'application principale
class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with VersionCheckMixin {
  @override
  void initState() {
    super.initState();
    // Vérifier la version au démarrage
    checkVersionOnStartup();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MbokaTour',
      navigatorKey: navigatorKey,
      home: const HomeScreen(),
    );
  }
}

// Clé globale pour le navigateur
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

/// Écran d'accueil exemple
class HomeScreen extends StatelessWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('MbokaTour'),
        actions: [
          IconButton(
            icon: const Icon(Icons.system_update),
            onPressed: () => _manualVersionCheck(context),
          ),
        ],
      ),
      body: const Center(
        child: Text('Bienvenue sur MbokaTour !'),
      ),
    );
  }

  /// Vérification manuelle de version
  void _manualVersionCheck(BuildContext context) async {
    try {
      final versionInfo = await VersionCheckService.checkVersion();
      
      if (versionInfo.hasUpdate) {
        showDialog(
          context: context,
          builder: (context) => UpdateDialog(versionInfo: versionInfo),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Vous avez la dernière version !'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
