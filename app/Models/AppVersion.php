<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Support\Facades\Storage;

class AppVersion extends Model
{
    use HasUuids;

    protected $fillable = [
        'platform',
        'version',
        'description',
        'file_path',
        'download_url',
        'file_size',
        'is_active',
        'force_update',
        'release_notes',
        'released_at',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'force_update' => 'boolean',
        'release_notes' => 'array',
        'released_at' => 'datetime',
        'file_size' => 'integer',
    ];

    protected $appends = [
        'formatted_file_size',
    ];

    /**
     * Scope pour récupérer les versions actives
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope pour récupérer les versions par plateforme
     */
    public function scopeForPlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * Récupérer la dernière version pour une plateforme
     */
    public static function getLatestVersion($platform)
    {
        return static::active()
            ->forPlatform($platform)
            ->orderBy('released_at', 'desc')
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * Obtenir l'URL de téléchargement finale
     */
    public function getFinalDownloadUrlAttribute()
    {
        // Si une URL externe est définie, l'utiliser
        if ($this->download_url) {
            return $this->download_url;
        }

        // Sinon, générer l'URL pour le fichier local
        if ($this->file_path) {
            return Storage::url($this->file_path);
        }

        return null;
    }

    /**
     * Obtenir la taille du fichier formatée
     */
    public function getFormattedFileSizeAttribute()
    {
        if (!$this->file_size) {
            return null;
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $unitIndex = 0;

        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }

        return round($size, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * Vérifier si c'est une version Android
     */
    public function isAndroid()
    {
        return $this->platform === 'android';
    }

    /**
     * Vérifier si c'est une version iOS
     */
    public function isIos()
    {
        return $this->platform === 'ios';
    }
}
