<?php

namespace App\Http\Controllers;

use App\Models\AppVersion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class DownloadController extends Controller
{
    /**
     * Afficher la page de téléchargement
     */
    public function index(Request $request)
    {
        // Détecter la plateforme depuis l'User-Agent ou le paramètre
        $platform = $this->detectPlatform($request);

        // Récupérer les dernières versions pour chaque plateforme
        $androidVersion = AppVersion::getLatestVersion('android');
        $iosVersion = AppVersion::getLatestVersion('ios');

        return Inertia::render('Download', [
            'detectedPlatform' => $platform,
            'androidVersion' => $androidVersion,
            'iosVersion' => $iosVersion,
        ]);
    }

    /**
     * Rediriger vers le téléchargement pour une plateforme spécifique
     */
    public function download(Request $request, $platform)
    {
        $version = AppVersion::getLatestVersion($platform);

        if (!$version) {
            return redirect()->route('download.index')
                ->with('error', 'Aucune version disponible pour cette plateforme.');
        }

        // Si c'est une URL externe (Play Store, App Store), rediriger
        if ($version->download_url && filter_var($version->download_url, FILTER_VALIDATE_URL)) {
            return redirect($version->download_url);
        }

        // Si c'est un fichier local, le télécharger
        if ($version->file_path && Storage::exists($version->file_path)) {
            return Storage::download($version->file_path, $this->getFileName($version));
        }

        return redirect()->route('download.index')
            ->with('error', 'Fichier de téléchargement non disponible.');
    }

    /**
     * Détecter la plateforme depuis l'User-Agent
     */
    private function detectPlatform(Request $request)
    {
        // Vérifier d'abord le paramètre de requête
        if ($request->has('platform')) {
            $platform = strtolower($request->get('platform'));
            if (in_array($platform, ['android', 'ios'])) {
                return $platform;
            }
        }

        $userAgent = $request->header('User-Agent', '');

        // Détecter iOS
        if (preg_match('/iPhone|iPad|iPod/i', $userAgent)) {
            return 'ios';
        }

        // Détecter Android
        if (preg_match('/Android/i', $userAgent)) {
            return 'android';
        }

        // Par défaut, retourner null pour afficher les deux options
        return null;
    }

    /**
     * Générer le nom du fichier pour le téléchargement
     */
    private function getFileName(AppVersion $version)
    {
        $extension = $version->isAndroid() ? 'apk' : 'ipa';
        return "MbokaTour-{$version->version}-{$version->platform}.{$extension}";
    }
}
