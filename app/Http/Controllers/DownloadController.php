<?php

namespace App\Http\Controllers;

use App\Models\AppVersion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class DownloadController extends Controller
{
    /**
     * Afficher la page de téléchargement
     */
    public function index(Request $request)
    {
        // Détecter la plateforme depuis l'User-Agent ou le paramètre
        $platform = $this->detectPlatform($request);

        // Récupérer les dernières versions pour chaque plateforme
        $androidVersion = AppVersion::getLatestVersion('android');
        $iosVersion = AppVersion::getLatestVersion('ios');

        return Inertia::render('Download', [
            'detectedPlatform' => $platform,
            'androidVersion' => $androidVersion,
            'iosVersion' => $iosVersion,
            'qrCodes' => [
                'android' => $androidVersion ? route('download.qr', 'android') : null,
                'ios' => $iosVersion ? route('download.qr', 'ios') : null,
            ],
        ]);
    }

    /**
     * Téléchargement direct ou page simple selon la plateforme
     */
    public function download(Request $request, $platform)
    {
        $version = AppVersion::getLatestVersion($platform);

        if (!$version) {
            return $this->showSimpleDownloadPage($platform, 'Aucune version disponible pour cette plateforme.');
        }

        // Si c'est une URL externe (Play Store, App Store), rediriger directement
        if ($version->download_url && filter_var($version->download_url, FILTER_VALIDATE_URL)) {
            return redirect($version->download_url);
        }

        // Si c'est un fichier local, afficher la page de téléchargement simple
        if ($version->file_path && Storage::exists($version->file_path)) {
            return $this->showSimpleDownloadPage($platform, null, $version);
        }

        return $this->showSimpleDownloadPage($platform, 'Fichier de téléchargement non disponible.');
    }

    /**
     * Téléchargement direct du fichier
     */
    public function downloadFile($platform)
    {
        $version = AppVersion::getLatestVersion($platform);

        if (!$version || !$version->file_path || !Storage::exists($version->file_path)) {
            abort(404, 'Fichier non trouvé');
        }

        return Storage::download($version->file_path, $this->getFileName($version));
    }

    /**
     * Afficher une page de téléchargement simple et minimaliste
     */
    private function showSimpleDownloadPage($platform, $error = null, $version = null)
    {
        return Inertia::render('SimpleDownload', [
            'platform' => $platform,
            'version' => $version,
            'error' => $error,
            'platformName' => $platform === 'android' ? 'Android' : 'iOS',
            'platformIcon' => $platform === 'android' ? '🤖' : '🍎',
        ]);
    }

    /**
     * Détecter la plateforme depuis l'User-Agent
     */
    private function detectPlatform(Request $request)
    {
        // Vérifier d'abord le paramètre de requête
        if ($request->has('platform')) {
            $platform = strtolower($request->get('platform'));
            if (in_array($platform, ['android', 'ios'])) {
                return $platform;
            }
        }

        $userAgent = $request->header('User-Agent', '');

        // Détecter iOS
        if (preg_match('/iPhone|iPad|iPod/i', $userAgent)) {
            return 'ios';
        }

        // Détecter Android
        if (preg_match('/Android/i', $userAgent)) {
            return 'android';
        }

        // Par défaut, retourner null pour afficher les deux options
        return null;
    }

    /**
     * Générer et afficher un QR code pour une plateforme
     */
    public function qrCode($platform)
    {
        $version = AppVersion::getLatestVersion($platform);

        if (!$version) {
            abort(404, 'Aucune version disponible pour cette plateforme');
        }

        $downloadUrl = url()->route('download.platform', $platform);

        return QrCode::format('svg')
            ->size(300)
            ->margin(2)
            ->style('round')
            ->eye('circle')
            ->gradient(
                '#fcc804', // Couleur primaire MbokaTour
                '#f59e0b', // Couleur secondaire
                'diagonal'
            )
            ->generate($downloadUrl);
    }

    /**
     * Télécharger le QR code en tant qu'image
     */
    public function downloadQrCode($platform)
    {
        $version = AppVersion::getLatestVersion($platform);

        if (!$version) {
            abort(404, 'Aucune version disponible pour cette plateforme');
        }

        $downloadUrl = url()->route('download.platform', $platform);
        $platformName = $platform === 'android' ? 'Android' : 'iOS';

        $qrCode = QrCode::format('png')
            ->size(512)
            ->margin(4)
            ->style('round')
            ->eye('circle')
            ->gradient(
                '#fcc804',
                '#f59e0b',
                'diagonal'
            )
            ->generate($downloadUrl);

        return response($qrCode)
            ->header('Content-Type', 'image/png')
            ->header('Content-Disposition', "attachment; filename=\"MbokaTour-QR-{$platformName}.png\"");
    }

    /**
     * Générer le nom du fichier pour le téléchargement
     */
    private function getFileName(AppVersion $version)
    {
        $extension = $version->isAndroid() ? 'apk' : 'ipa';
        return "MbokaTour-{$version->version}-{$version->platform}.{$extension}";
    }
}
