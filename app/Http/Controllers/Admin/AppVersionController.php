<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AppVersion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class AppVersionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $versions = AppVersion::orderBy('platform')
            ->orderBy('released_at', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('Admin/AppVersions/Index', [
            'versions' => $versions,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/AppVersions/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'platform' => ['required', Rule::in(['android', 'ios'])],
            'version' => 'required|string|max:50',
            'description' => 'nullable|string|max:500',
            'download_url' => 'nullable|url|max:500',
            'app_file' => 'nullable|file|max:102400', // 100MB max
            'is_active' => 'boolean',
            'force_update' => 'boolean',
            'release_notes' => 'nullable|array',
            'release_notes.*' => 'string|max:500',
            'released_at' => 'nullable|date',
        ]);

        // Validation du fichier selon la plateforme
        if ($request->hasFile('app_file')) {
            $file = $request->file('app_file');
            $extension = $file->getClientOriginalExtension();

            if ($validated['platform'] === 'android' && $extension !== 'apk') {
                return back()->withErrors(['app_file' => 'Le fichier doit être un APK pour Android.']);
            }

            if ($validated['platform'] === 'ios' && $extension !== 'ipa') {
                return back()->withErrors(['app_file' => 'Le fichier doit être un IPA pour iOS.']);
            }
        }

        // Désactiver les autres versions si celle-ci est active
        if ($validated['is_active'] ?? false) {
            AppVersion::where('platform', $validated['platform'])
                ->where('is_active', true)
                ->update(['is_active' => false]);
        }

        $appVersion = new AppVersion($validated);

        // Upload du fichier
        if ($request->hasFile('app_file')) {
            $file = $request->file('app_file');
            $filename = "app-{$validated['platform']}-{$validated['version']}.{$file->getClientOriginalExtension()}";
            $path = $file->storeAs('app-versions', $filename, 'public');

            $appVersion->file_path = $path;
            $appVersion->file_size = $file->getSize();
        }

        $appVersion->save();

        return redirect()->route('admin.app-versions.index')
            ->with('success', 'Version d\'application créée avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(AppVersion $appVersion)
    {
        return Inertia::render('Admin/AppVersions/Show', [
            'version' => $appVersion,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AppVersion $appVersion)
    {
        return Inertia::render('Admin/AppVersions/Edit', [
            'version' => $appVersion,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AppVersion $appVersion)
    {
        $validated = $request->validate([
            'platform' => ['required', Rule::in(['android', 'ios'])],
            'version' => 'required|string|max:50',
            'description' => 'nullable|string|max:500',
            'download_url' => 'nullable|url|max:500',
            'app_file' => 'nullable|file|max:102400', // 100MB max
            'is_active' => 'boolean',
            'force_update' => 'boolean',
            'release_notes' => 'nullable|array',
            'release_notes.*' => 'string|max:500',
            'released_at' => 'nullable|date',
        ]);

        // Validation du fichier selon la plateforme
        if ($request->hasFile('app_file')) {
            $file = $request->file('app_file');
            $extension = $file->getClientOriginalExtension();

            if ($validated['platform'] === 'android' && $extension !== 'apk') {
                return back()->withErrors(['app_file' => 'Le fichier doit être un APK pour Android.']);
            }

            if ($validated['platform'] === 'ios' && $extension !== 'ipa') {
                return back()->withErrors(['app_file' => 'Le fichier doit être un IPA pour iOS.']);
            }
        }

        // Désactiver les autres versions si celle-ci est active
        if ($validated['is_active'] ?? false) {
            AppVersion::where('platform', $validated['platform'])
                ->where('is_active', true)
                ->where('id', '!=', $appVersion->id)
                ->update(['is_active' => false]);
        }

        // Upload du nouveau fichier
        if ($request->hasFile('app_file')) {
            // Supprimer l'ancien fichier
            if ($appVersion->file_path && Storage::disk('public')->exists($appVersion->file_path)) {
                Storage::disk('public')->delete($appVersion->file_path);
            }

            $file = $request->file('app_file');
            $filename = "app-{$validated['platform']}-{$validated['version']}.{$file->getClientOriginalExtension()}";
            $path = $file->storeAs('app-versions', $filename, 'public');

            $validated['file_path'] = $path;
            $validated['file_size'] = $file->getSize();
        }

        $appVersion->update($validated);

        return redirect()->route('admin.app-versions.index')
            ->with('success', 'Version d\'application mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AppVersion $appVersion)
    {
        // Supprimer le fichier associé
        if ($appVersion->file_path && Storage::disk('public')->exists($appVersion->file_path)) {
            Storage::disk('public')->delete($appVersion->file_path);
        }

        $appVersion->delete();

        return redirect()->route('admin.app-versions.index')
            ->with('success', 'Version d\'application supprimée avec succès.');
    }
}
