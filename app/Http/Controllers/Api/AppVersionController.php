<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AppVersionController extends Controller
{
    /**
     * Vérifie la version de l'application et retourne les informations de mise à jour
     */
    public function checkVersion(Request $request): JsonResponse
    {
        // Validation des paramètres requis
        $request->validate([
            'current_version' => 'required|string|regex:/^\d+\.\d+\.\d+$/',
            'platform' => 'required|string|in:android,ios'
        ]);

        $currentVersion = $request->query('current_version');
        $platform = $request->query('platform');
        
        // Configuration des versions par plateforme
        $config = $this->getVersionConfig();
        $platformConfig = $config[$platform] ?? $config['android'];
        
        // Comparaison des versions
        $forceUpdate = version_compare($currentVersion, $platformConfig['minimum_version'], '<');
        $hasUpdate = version_compare($currentVersion, $platformConfig['latest_version'], '<');
        
        // Préparation de la réponse
        $response = [
            'minimum_version' => $platformConfig['minimum_version'],
            'latest_version' => $platformConfig['latest_version'],
            'force_update' => $forceUpdate,
            'has_update' => $hasUpdate,
            'download_url' => $platformConfig['download_url'],
        ];

        // Messages et fonctionnalités selon le type de mise à jour
        if ($forceUpdate) {
            $response['update_message'] = 'Une mise à jour obligatoire est disponible pour continuer à utiliser MbokaTour.';
            $response['features'] = $this->getCriticalUpdateFeatures();
        } elseif ($hasUpdate) {
            $response['update_message'] = 'Une nouvelle version de MbokaTour est disponible avec de nouvelles fonctionnalités !';
            $response['features'] = $this->getOptionalUpdateFeatures();
        } else {
            $response['update_message'] = null;
            $response['features'] = null;
        }

        return response()->json($response);
    }

    /**
     * Configuration des versions par plateforme
     */
    private function getVersionConfig(): array
    {
        return [
            'android' => [
                'minimum_version' => config('app.android_minimum_version', '1.0.0'),
                'latest_version' => config('app.android_latest_version', '1.1.0'),
                'download_url' => config('app.android_store_url', 'https://play.google.com/store/apps/details?id=com.mbokatour.app')
            ],
            'ios' => [
                'minimum_version' => config('app.ios_minimum_version', '1.0.0'),
                'latest_version' => config('app.ios_latest_version', '1.1.0'),
                'download_url' => config('app.ios_store_url', 'https://apps.apple.com/app/mbokatour/id123456789')
            ]
        ];
    }

    /**
     * Fonctionnalités pour les mises à jour critiques (force_update)
     */
    private function getCriticalUpdateFeatures(): array
    {
        return [
            'Corrections de sécurité importantes',
            'Résolution de bugs critiques',
            'Améliorations de performance essentielles'
        ];
    }

    /**
     * Fonctionnalités pour les mises à jour optionnelles
     */
    private function getOptionalUpdateFeatures(): array
    {
        return [
            'Nouvelles destinations à découvrir',
            'Interface utilisateur améliorée',
            'Nouvelles fonctionnalités de recherche',
            'Optimisations de performance',
            'Corrections de bugs mineurs'
        ];
    }
}
