<script setup lang="ts">
import { Head, Link, router } from '@inertiajs/vue3';

interface AppVersion {
    id: string;
    platform: string;
    version: string;
    description?: string;
    download_url?: string;
    file_path?: string;
    file_size?: number;
    formatted_file_size?: string;
    is_active: boolean;
    force_update: boolean;
    release_notes?: string[];
    released_at?: string;
    created_at: string;
    updated_at: string;
}

interface Props {
    version: AppVersion;
}

const props = defineProps<Props>();

const deleteVersion = () => {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la version ${props.version.version} pour ${props.version.platform} ?`)) {
        router.delete(route('admin.app-versions.destroy', props.version.id));
    }
};

const getPlatformIcon = (platform: string) => {
    return platform === 'android' ? '🤖' : '🍎';
};

const getPlatformName = (platform: string) => {
    return platform === 'android' ? 'Android' : 'iOS';
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};
</script>

<template>
    <Head :title="`Version ${version.version} - ${getPlatformName(version.platform)}`" />

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <span class="text-4xl">{{ getPlatformIcon(version.platform) }}</span>
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900">
                                    {{ getPlatformName(version.platform) }} - Version {{ version.version }}
                                </h2>
                                <div class="flex items-center space-x-4 mt-1">
                                    <span v-if="version.is_active" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Active
                                    </span>
                                    <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        Inactive
                                    </span>
                                    <span v-if="version.force_update" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                        Mise à jour forcée
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <Link
                                :href="route('admin.app-versions.edit', version.id)"
                                class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                                Modifier
                            </Link>
                            <button
                                @click="deleteVersion"
                                class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                </svg>
                                Supprimer
                            </button>
                            <Link
                                :href="route('admin.app-versions.index')"
                                class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                                </svg>
                                Retour
                            </Link>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Version Details -->
            <div class="grid md:grid-cols-2 gap-6 mb-6">
                <!-- Basic Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Informations générales</h3>
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Plateforme</dt>
                                <dd class="mt-1 text-sm text-gray-900 flex items-center">
                                    <span class="mr-2">{{ getPlatformIcon(version.platform) }}</span>
                                    {{ getPlatformName(version.platform) }}
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Version</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ version.version }}</dd>
                            </div>
                            <div v-if="version.description">
                                <dt class="text-sm font-medium text-gray-500">Description</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ version.description }}</dd>
                            </div>
                            <div v-if="version.released_at">
                                <dt class="text-sm font-medium text-gray-500">Date de sortie</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(version.released_at) }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Créé le</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(version.created_at) }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Modifié le</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(version.updated_at) }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Download Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Téléchargement</h3>
                        <dl class="space-y-4">
                            <div v-if="version.download_url">
                                <dt class="text-sm font-medium text-gray-500">URL externe</dt>
                                <dd class="mt-1">
                                    <a 
                                        :href="version.download_url" 
                                        target="_blank"
                                        class="text-sm text-primary-600 hover:text-primary-800 underline break-all"
                                    >
                                        {{ version.download_url }}
                                    </a>
                                </dd>
                            </div>
                            <div v-if="version.file_path">
                                <dt class="text-sm font-medium text-gray-500">Fichier uploadé</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ version.file_path }}</dd>
                            </div>
                            <div v-if="version.formatted_file_size">
                                <dt class="text-sm font-medium text-gray-500">Taille du fichier</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ version.formatted_file_size }}</dd>
                            </div>
                            <div v-if="!version.download_url && !version.file_path">
                                <div class="text-sm text-gray-500 italic">
                                    Aucun fichier ou URL de téléchargement configuré
                                </div>
                            </div>
                        </dl>

                        <!-- Download Button -->
                        <div v-if="version.download_url || version.file_path" class="mt-6 space-y-3">
                            <a
                                :href="route('download.platform', version.platform)"
                                target="_blank"
                                class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                Tester le téléchargement
                            </a>

                            <div class="flex space-x-2">
                                <a
                                    :href="route('download.qr', version.platform)"
                                    target="_blank"
                                    class="inline-flex items-center px-3 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                                    </svg>
                                    Voir QR Code
                                </a>

                                <a
                                    :href="route('download.qr.download', version.platform)"
                                    class="inline-flex items-center px-3 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700"
                                >
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                    Télécharger QR
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">QR Code de téléchargement</h3>
                    <div class="flex flex-col md:flex-row md:items-center md:space-x-6">
                        <div class="mb-4 md:mb-0">
                            <div class="bg-gray-50 p-4 rounded-lg inline-block">
                                <img
                                    :src="route('download.qr', version.platform)"
                                    :alt="`QR Code ${version.platform}`"
                                    class="w-32 h-32"
                                />
                            </div>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900 mb-2">Partage facile</h4>
                            <p class="text-sm text-gray-600 mb-4">
                                Ce QR code permet aux utilisateurs de télécharger directement l'application
                                {{ getPlatformName(version.platform) }} en scannant avec leur téléphone.
                            </p>
                            <div class="flex space-x-2">
                                <a
                                    :href="route('download.qr', version.platform)"
                                    target="_blank"
                                    class="inline-flex items-center px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"
                                >
                                    Voir en grand
                                </a>
                                <a
                                    :href="route('download.qr.download', version.platform)"
                                    class="inline-flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                                >
                                    Télécharger PNG
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Release Notes -->
            <div v-if="version.release_notes && version.release_notes.length > 0" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Notes de version</h3>
                    <ul class="space-y-2">
                        <li v-for="(note, index) in version.release_notes" :key="index" class="flex items-start">
                            <span class="flex-shrink-0 w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3"></span>
                            <span class="text-sm text-gray-700">{{ note }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</template>
