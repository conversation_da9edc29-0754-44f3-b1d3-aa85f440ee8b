<script setup lang="ts">
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

interface AppVersion {
    id: string;
    platform: string;
    version: string;
    description?: string;
    download_url?: string;
    file_path?: string;
    file_size?: number;
    formatted_file_size?: string;
    is_active: boolean;
    force_update: boolean;
    release_notes?: string[];
    released_at?: string;
    created_at: string;
    updated_at: string;
}

interface Props {
    version: AppVersion;
}

const props = defineProps<Props>();

const form = useForm({
    platform: props.version.platform,
    version: props.version.version,
    description: props.version.description || '',
    download_url: props.version.download_url || '',
    app_file: null as File | null,
    is_active: props.version.is_active,
    force_update: props.version.force_update,
    release_notes: props.version.release_notes && props.version.release_notes.length > 0 ? props.version.release_notes : [''],
    released_at: props.version.released_at ? props.version.released_at.slice(0, 16) : '',
});

const fileInput = ref<HTMLInputElement>();

const addReleaseNote = () => {
    form.release_notes.push('');
};

const removeReleaseNote = (index: number) => {
    if (form.release_notes.length > 1) {
        form.release_notes.splice(index, 1);
    }
};

const handleFileChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
        const file = target.files[0];
        const maxSize = 100 * 1024 * 1024; // 100MB en bytes

        if (file.size > maxSize) {
            alert(`Le fichier est trop volumineux (${Math.round(file.size / 1024 / 1024)}MB). Taille maximale autorisée : 100MB`);
            target.value = ''; // Reset le champ
            return;
        }

        form.app_file = file;
    }
};

const submit = () => {
    // Nettoyer les notes de version vides
    form.release_notes = form.release_notes.filter(note => note.trim() !== '');
    
    form.post(route('admin.app-versions.update', props.version.id), {
        forceFormData: true,
    });
};
</script>

<template>
    <Head title="Modifier la version d'application" />

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">Modifier la version d'application</h2>
                            <p class="text-gray-600 mt-1">
                                {{ version.platform === 'android' ? '🤖 Android' : '🍎 iOS' }} - Version {{ version.version }}
                            </p>
                        </div>
                        <div class="flex space-x-2">
                            <Link
                                :href="route('admin.app-versions.show', version.id)"
                                class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700"
                            >
                                Voir
                            </Link>
                            <Link
                                :href="route('admin.app-versions.index')"
                                class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                                </svg>
                                Retour
                            </Link>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current File Info -->
            <div v-if="version.file_path || version.download_url" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h3 class="text-sm font-medium text-blue-800 mb-2">Fichier actuel</h3>
                <div class="text-sm text-blue-700">
                    <div v-if="version.file_path" class="flex items-center space-x-2">
                        <span>📁 Fichier uploadé:</span>
                        <span class="font-mono">{{ version.file_path }}</span>
                        <span v-if="version.formatted_file_size" class="text-blue-600">({{ version.formatted_file_size }})</span>
                    </div>
                    <div v-if="version.download_url" class="flex items-center space-x-2 mt-1">
                        <span>🔗 URL externe:</span>
                        <a :href="version.download_url" target="_blank" class="text-blue-600 hover:text-blue-800 underline">
                            {{ version.download_url }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Form -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <form @submit.prevent="submit" class="p-6 space-y-6">
                    <!-- Platform -->
                    <div>
                        <label for="platform" class="block text-sm font-medium text-gray-700 mb-2">
                            Plateforme *
                        </label>
                        <select
                            id="platform"
                            v-model="form.platform"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            required
                        >
                            <option value="">Sélectionnez une plateforme</option>
                            <option value="android">🤖 Android</option>
                            <option value="ios">🍎 iOS</option>
                        </select>
                        <div v-if="form.errors.platform" class="mt-1 text-sm text-red-600">
                            {{ form.errors.platform }}
                        </div>
                    </div>

                    <!-- Version -->
                    <div>
                        <label for="version" class="block text-sm font-medium text-gray-700 mb-2">
                            Version *
                        </label>
                        <input
                            id="version"
                            v-model="form.version"
                            type="text"
                            placeholder="ex: 1.0.0"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            required
                        />
                        <div v-if="form.errors.version" class="mt-1 text-sm text-red-600">
                            {{ form.errors.version }}
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Description
                        </label>
                        <textarea
                            id="description"
                            v-model="form.description"
                            rows="3"
                            placeholder="Description de cette version..."
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        ></textarea>
                        <div v-if="form.errors.description" class="mt-1 text-sm text-red-600">
                            {{ form.errors.description }}
                        </div>
                    </div>

                    <!-- Download URL -->
                    <div>
                        <label for="download_url" class="block text-sm font-medium text-gray-700 mb-2">
                            URL de téléchargement externe
                        </label>
                        <input
                            id="download_url"
                            v-model="form.download_url"
                            type="url"
                            placeholder="https://play.google.com/store/apps/details?id=..."
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        />
                        <p class="mt-1 text-sm text-gray-500">
                            URL vers le Play Store ou App Store. Laissez vide si vous uploadez un fichier.
                        </p>
                        <div v-if="form.errors.download_url" class="mt-1 text-sm text-red-600">
                            {{ form.errors.download_url }}
                        </div>
                    </div>

                    <!-- File Upload -->
                    <div>
                        <label for="app_file" class="block text-sm font-medium text-gray-700 mb-2">
                            Nouveau fichier d'application
                        </label>
                        <input
                            ref="fileInput"
                            id="app_file"
                            type="file"
                            :accept="form.platform === 'android' ? '.apk' : form.platform === 'ios' ? '.ipa' : '.apk,.ipa'"
                            @change="handleFileChange"
                            class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                        />
                        <p class="mt-1 text-sm text-gray-500">
                            Uploadez un nouveau fichier APK (Android) ou IPA (iOS) pour remplacer l'actuel. Maximum 100MB.
                        </p>
                        <div v-if="form.errors.app_file" class="mt-1 text-sm text-red-600">
                            {{ form.errors.app_file }}
                        </div>
                    </div>

                    <!-- Release Date -->
                    <div>
                        <label for="released_at" class="block text-sm font-medium text-gray-700 mb-2">
                            Date de sortie
                        </label>
                        <input
                            id="released_at"
                            v-model="form.released_at"
                            type="datetime-local"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        />
                        <div v-if="form.errors.released_at" class="mt-1 text-sm text-red-600">
                            {{ form.errors.released_at }}
                        </div>
                    </div>

                    <!-- Release Notes -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Notes de version
                        </label>
                        <div class="space-y-2">
                            <div v-for="(note, index) in form.release_notes" :key="index" class="flex items-center space-x-2">
                                <input
                                    v-model="form.release_notes[index]"
                                    type="text"
                                    placeholder="Nouvelle fonctionnalité ou correction..."
                                    class="flex-1 border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                />
                                <button
                                    v-if="form.release_notes.length > 1"
                                    type="button"
                                    @click="removeReleaseNote(index)"
                                    class="p-2 text-red-600 hover:text-red-800"
                                >
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <button
                            type="button"
                            @click="addReleaseNote"
                            class="mt-2 inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 bg-white hover:bg-gray-50"
                        >
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                            </svg>
                            Ajouter une note
                        </button>
                        <div v-if="form.errors['release_notes.0']" class="mt-1 text-sm text-red-600">
                            {{ form.errors['release_notes.0'] }}
                        </div>
                    </div>

                    <!-- Options -->
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input
                                id="is_active"
                                v-model="form.is_active"
                                type="checkbox"
                                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            />
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Version active (désactivera les autres versions de cette plateforme)
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input
                                id="force_update"
                                v-model="form.force_update"
                                type="checkbox"
                                class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                            />
                            <label for="force_update" class="ml-2 block text-sm text-gray-900">
                                Forcer la mise à jour (les utilisateurs devront obligatoirement mettre à jour)
                            </label>
                        </div>
                    </div>

                    <!-- Submit -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                        <Link
                            :href="route('admin.app-versions.index')"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-50"
                        >
                            Annuler
                        </Link>
                        <button
                            type="submit"
                            :disabled="form.processing"
                            class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 active:bg-primary-900 focus:outline-none focus:border-primary-900 focus:ring ring-primary-300 disabled:opacity-25 transition ease-in-out duration-150"
                        >
                            <svg v-if="form.processing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ form.processing ? 'Mise à jour...' : 'Mettre à jour' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>
