<script setup lang="ts">
import { Head, <PERSON> } from '@inertiajs/vue3';
import { computed } from 'vue';

interface AppVersion {
    id: string;
    platform: string;
    version: string;
    description?: string;
    file_path?: string;
    file_size?: number;
    formatted_file_size?: string;
}

interface Props {
    platform: string;
    version?: AppVersion | null;
    error?: string | null;
    platformName: string;
    platformIcon: string;
}

const props = defineProps<Props>();

// Détecter si c'est un appareil mobile
const isMobile = computed(() => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
});

// Choisir la bonne route de téléchargement
const downloadUrl = computed(() => {
    if (isMobile.value && props.platform === 'android') {
        // Utiliser la route directe pour Android mobile
        return route('download.file.direct', props.platform);
    }
    // Route normale pour desktop et iOS
    return route('download.file', props.platform);
});

// Pas de téléchargement automatique - l'utilisateur doit lire les instructions d'abord
</script>

<template>
    <Head :title="`Télécharger MbokaTour ${platformName}`">
        <meta name="description" :content="`Téléchargez MbokaTour pour ${platformName}`" />
        <meta name="robots" content="noindex,nofollow" />
    </Head>

    <div class="min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-6">
        <!-- Animated Background Shapes -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-200/30 rounded-full blur-3xl animate-pulse dark:bg-primary-800/20"></div>
            <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-primary-300/20 rounded-full blur-3xl animate-pulse delay-1000 dark:bg-primary-700/10"></div>
        </div>

        <div class="relative z-10 max-w-md w-full">
            <!-- Card -->
            <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 p-8 text-center dark:bg-gray-800/80 dark:border-gray-700/50">
                <!-- Platform Icon -->
                <div class="text-8xl mb-6">{{ platformIcon }}</div>
                
                <!-- Title -->
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    MbokaTour {{ platformName }}
                </h1>

                <!-- Error State -->
                <div v-if="error" class="mb-6">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                        <p class="text-red-800 text-sm">{{ error }}</p>
                    </div>
                    <Link
                        :href="route('home')"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                        </svg>
                        Retour à l'accueil
                    </Link>
                </div>

                <!-- Success State -->
                <div v-else-if="version" class="space-y-6">
                    <!-- Version Info -->
                    <div class="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
                        <p class="text-primary-800 dark:text-primary-200 font-medium">
                            Version {{ version.version }}
                        </p>
                        <p v-if="version.formatted_file_size" class="text-primary-600 dark:text-primary-300 text-sm">
                            {{ version.formatted_file_size }}
                        </p>
                    </div>

                    <!-- Installation Instructions (en premier) -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 text-left">
                        <h3 class="font-bold text-blue-900 dark:text-blue-100 mb-4 text-center flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            Instructions d'installation
                        </h3>

                        <div v-if="platform === 'android'" class="space-y-3">
                            <div class="flex items-start space-x-3">
                                <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</span>
                                <div>
                                    <p class="font-medium text-blue-900 dark:text-blue-100">Autoriser les sources inconnues</p>
                                    <p class="text-sm text-blue-700 dark:text-blue-300">Allez dans Paramètres > Sécurité > Sources inconnues</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</span>
                                <div>
                                    <p class="font-medium text-blue-900 dark:text-blue-100">Télécharger l'application</p>
                                    <p class="text-sm text-blue-700 dark:text-blue-300">Cliquez sur le bouton de téléchargement ci-dessous</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</span>
                                <div>
                                    <p class="font-medium text-blue-900 dark:text-blue-100">Installer l'APK</p>
                                    <p class="text-sm text-blue-700 dark:text-blue-300">Ouvrez le fichier téléchargé et appuyez sur "Installer"</p>
                                </div>
                            </div>
                        </div>

                        <div v-else class="space-y-3">
                            <div class="flex items-start space-x-3">
                                <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</span>
                                <div>
                                    <p class="font-medium text-blue-900 dark:text-blue-100">Télécharger l'application</p>
                                    <p class="text-sm text-blue-700 dark:text-blue-300">Cliquez sur le bouton de téléchargement ci-dessous</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</span>
                                <div>
                                    <p class="font-medium text-blue-900 dark:text-blue-100">Installer l'IPA</p>
                                    <p class="text-sm text-blue-700 dark:text-blue-300">Suivez les instructions d'installation iOS</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</span>
                                <div>
                                    <p class="font-medium text-blue-900 dark:text-blue-100">Autoriser l'application</p>
                                    <p class="text-sm text-blue-700 dark:text-blue-300">Allez dans Réglages > Général > Gestion des appareils</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Download Button (plus visible) -->
                    <div class="text-center">
                        <a
                            :href="downloadUrl"
                            class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl font-bold text-lg"
                        >
                            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            Télécharger MbokaTour
                        </a>

                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-3">
                            Lisez les instructions ci-dessus avant de télécharger
                        </p>

                        <!-- Message spécifique pour mobile Android -->
                        <div v-if="isMobile && platform === 'android'" class="mt-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                            <p class="text-sm text-amber-800 dark:text-amber-200 flex items-center">
                                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                                <span><strong>Mobile détecté :</strong> Le fichier sera téléchargé avec l'extension .apk correcte</span>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
                    <Link
                        :href="route('home')"
                        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-sm transition-colors"
                    >
                        ← Retour à l'accueil
                    </Link>
                </div>
            </div>
        </div>
    </div>
</template>
