<script setup lang="ts">
import { Head, Link } from '@inertiajs/vue3';
import { onMounted } from 'vue';

interface AppVersion {
    id: string;
    platform: string;
    version: string;
    description?: string;
    file_path?: string;
    file_size?: number;
    formatted_file_size?: string;
}

interface Props {
    platform: string;
    version?: AppVersion | null;
    error?: string | null;
    platformName: string;
    platformIcon: string;
}

const props = defineProps<Props>();

// Démarrer automatiquement le téléchargement si un fichier est disponible
onMounted(() => {
    if (props.version && props.version.file_path && !props.error) {
        // Attendre un peu pour que l'utilisateur voie la page
        setTimeout(() => {
            window.location.href = route('download.file', props.platform);
        }, 2000);
    }
});
</script>

<template>
    <Head :title="`Télécharger MbokaTour ${platformName}`">
        <meta name="description" :content="`Téléchargez MbokaTour pour ${platformName}`" />
        <meta name="robots" content="noindex,nofollow" />
    </Head>

    <div class="min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-6">
        <!-- Animated Background Shapes -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-200/30 rounded-full blur-3xl animate-pulse dark:bg-primary-800/20"></div>
            <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-primary-300/20 rounded-full blur-3xl animate-pulse delay-1000 dark:bg-primary-700/10"></div>
        </div>

        <div class="relative z-10 max-w-md w-full">
            <!-- Card -->
            <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 p-8 text-center dark:bg-gray-800/80 dark:border-gray-700/50">
                <!-- Platform Icon -->
                <div class="text-8xl mb-6">{{ platformIcon }}</div>
                
                <!-- Title -->
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    MbokaTour {{ platformName }}
                </h1>

                <!-- Error State -->
                <div v-if="error" class="mb-6">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                        <p class="text-red-800 text-sm">{{ error }}</p>
                    </div>
                    <Link
                        :href="route('home')"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                        </svg>
                        Retour à l'accueil
                    </Link>
                </div>

                <!-- Success State -->
                <div v-else-if="version" class="space-y-6">
                    <!-- Version Info -->
                    <div class="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
                        <p class="text-primary-800 dark:text-primary-200 font-medium">
                            Version {{ version.version }}
                        </p>
                        <p v-if="version.formatted_file_size" class="text-primary-600 dark:text-primary-300 text-sm">
                            {{ version.formatted_file_size }}
                        </p>
                    </div>

                    <!-- Download Status -->
                    <div class="space-y-4">
                        <div class="flex items-center justify-center space-x-2 text-green-600">
                            <svg class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                            <span class="font-medium">Téléchargement en cours...</span>
                        </div>
                        
                        <p class="text-gray-600 dark:text-gray-300 text-sm">
                            Le téléchargement va commencer automatiquement dans quelques secondes.
                        </p>
                    </div>

                    <!-- Manual Download Button -->
                    <div class="space-y-4">
                        <a
                            :href="route('download.file', platform)"
                            class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
                        >
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            Télécharger maintenant
                        </a>
                        
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            Si le téléchargement ne démarre pas automatiquement
                        </p>
                    </div>

                    <!-- Installation Instructions -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-left">
                        <h3 class="font-medium text-gray-900 dark:text-white mb-2 text-center">
                            📋 Installation
                        </h3>
                        <ol v-if="platform === 'android'" class="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                            <li>1. Autorisez les sources inconnues si demandé</li>
                            <li>2. Ouvrez le fichier APK téléchargé</li>
                            <li>3. Appuyez sur "Installer"</li>
                        </ol>
                        <ol v-else class="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                            <li>1. Ouvrez le fichier IPA téléchargé</li>
                            <li>2. Suivez les instructions d'installation</li>
                            <li>3. Autorisez l'application dans Réglages</li>
                        </ol>
                    </div>
                </div>

                <!-- Footer -->
                <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
                    <Link
                        :href="route('home')"
                        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-sm transition-colors"
                    >
                        ← Retour à l'accueil
                    </Link>
                </div>
            </div>
        </div>
    </div>
</template>
