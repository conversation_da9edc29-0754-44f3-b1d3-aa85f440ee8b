<script setup lang="ts">
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';

interface AppVersion {
    id: string;
    platform: string;
    version: string;
    description?: string;
    download_url?: string;
    file_size?: number;
    formatted_file_size?: string;
    force_update: boolean;
    release_notes?: string[];
    released_at?: string;
}

interface Props {
    detectedPlatform?: string | null;
    androidVersion?: AppVersion | null;
    iosVersion?: AppVersion | null;
}

const props = defineProps<Props>();

const getPlatformIcon = (platform: string) => {
    return platform === 'android' ? '🤖' : '🍎';
};

const getPlatformName = (platform: string) => {
    return platform === 'android' ? 'Android' : 'iOS';
};

const getStoreIcon = (platform: string) => {
    if (platform === 'android') {
        return `<svg class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
        </svg>`;
    } else {
        return `<svg class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 ********** 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
        </svg>`;
    }
};
</script>

<template>
    <Head title="Télécharger MbokaTour">
        <meta name="description" content="Téléchargez l'application MbokaTour pour Android et iOS. Redécouvrez Kinshasa autrement avec notre guide mobile." />
        <meta name="robots" content="index,follow" />
    </Head>

    <div class="relative flex min-h-screen flex-col items-center p-6 text-[#1b1b18] lg:justify-center lg:p-8 overflow-hidden">
        <!-- Background Elements (same as Welcome page) -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-primary-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900"></div>

        <!-- Animated Background Shapes -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-200/30 rounded-full blur-3xl animate-pulse dark:bg-primary-800/20"></div>
            <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-primary-300/20 rounded-full blur-3xl animate-pulse delay-1000 dark:bg-primary-700/10"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-primary-100/40 rounded-full blur-2xl animate-pulse delay-500 dark:bg-primary-900/20"></div>
            <div class="absolute top-1/4 right-1/4 w-48 h-48 bg-primary-200/25 rounded-full blur-2xl animate-pulse delay-700 dark:bg-primary-800/15"></div>
            <div class="absolute bottom-1/4 left-1/4 w-56 h-56 bg-primary-300/30 rounded-full blur-3xl animate-pulse delay-300 dark:bg-primary-700/15"></div>
        </div>

        <!-- Subtle Pattern Overlay -->
        <div class="absolute inset-0 opacity-30">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-primary-100/20 to-transparent dark:via-primary-800/10"></div>
        </div>

        <!-- Main Content -->
        <div class="relative z-10 w-full max-w-4xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-12">
                <Link :href="route('home')" class="inline-flex items-center text-primary-600 hover:text-primary-700 mb-6 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    Retour à l'accueil
                </Link>
                
                <h1 class="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl md:text-6xl dark:text-white mb-4">
                    <span class="block bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600 bg-clip-text text-transparent">
                        Télécharger MbokaTour
                    </span>
                </h1>
                
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    Choisissez votre plateforme et commencez à redécouvrir Kinshasa dès maintenant
                </p>
            </div>

            <!-- Platform Detection Alert -->
            <div v-if="detectedPlatform" class="mb-8 p-4 bg-primary-50 border border-primary-200 rounded-lg dark:bg-primary-900/20 dark:border-primary-800">
                <div class="flex items-center">
                    <span class="text-2xl mr-3">{{ getPlatformIcon(detectedPlatform) }}</span>
                    <div>
                        <p class="text-primary-800 dark:text-primary-200 font-medium">
                            Nous avons détecté que vous utilisez {{ getPlatformName(detectedPlatform) }}
                        </p>
                        <p class="text-primary-600 dark:text-primary-300 text-sm">
                            Cliquez sur le bouton ci-dessous pour télécharger directement
                        </p>
                    </div>
                </div>
            </div>

            <!-- Download Options -->
            <div class="grid md:grid-cols-2 gap-8 mb-12">
                <!-- Android Card -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 p-8 dark:bg-gray-800/80 dark:border-gray-700/50">
                    <div class="text-center mb-6">
                        <div class="text-6xl mb-4">🤖</div>
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Android</h2>
                        <p class="text-gray-600 dark:text-gray-300">Pour smartphones et tablettes Android</p>
                    </div>

                    <div v-if="androidVersion" class="space-y-4">
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium text-gray-900 dark:text-white">Version</span>
                                <span class="text-primary-600 dark:text-primary-400 font-semibold">{{ androidVersion.version }}</span>
                            </div>
                            <div v-if="androidVersion.formatted_file_size" class="flex justify-between items-center mb-2">
                                <span class="font-medium text-gray-900 dark:text-white">Taille</span>
                                <span class="text-gray-600 dark:text-gray-300">{{ androidVersion.formatted_file_size }}</span>
                            </div>
                            <div v-if="androidVersion.released_at" class="flex justify-between items-center">
                                <span class="font-medium text-gray-900 dark:text-white">Sortie</span>
                                <span class="text-gray-600 dark:text-gray-300">{{ new Date(androidVersion.released_at).toLocaleDateString('fr-FR') }}</span>
                            </div>
                        </div>

                        <a 
                            :href="route('download.platform', 'android')"
                            class="group w-full inline-flex items-center justify-center px-6 py-4 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                        >
                            <span v-html="getStoreIcon('android')" class="mr-3"></span>
                            <div class="text-left">
                                <div class="text-sm">Télécharger pour</div>
                                <div class="font-semibold">Android</div>
                            </div>
                        </a>

                        <div v-if="androidVersion.description" class="text-sm text-gray-600 dark:text-gray-400">
                            {{ androidVersion.description }}
                        </div>
                    </div>

                    <div v-else class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400 mb-4">Version Android non disponible</p>
                        <div class="text-sm text-gray-400 dark:text-gray-500">
                            La version Android sera bientôt disponible
                        </div>
                    </div>
                </div>

                <!-- iOS Card -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 p-8 dark:bg-gray-800/80 dark:border-gray-700/50">
                    <div class="text-center mb-6">
                        <div class="text-6xl mb-4">🍎</div>
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">iOS</h2>
                        <p class="text-gray-600 dark:text-gray-300">Pour iPhone et iPad</p>
                    </div>

                    <div v-if="iosVersion" class="space-y-4">
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium text-gray-900 dark:text-white">Version</span>
                                <span class="text-primary-600 dark:text-primary-400 font-semibold">{{ iosVersion.version }}</span>
                            </div>
                            <div v-if="iosVersion.formatted_file_size" class="flex justify-between items-center mb-2">
                                <span class="font-medium text-gray-900 dark:text-white">Taille</span>
                                <span class="text-gray-600 dark:text-gray-300">{{ iosVersion.formatted_file_size }}</span>
                            </div>
                            <div v-if="iosVersion.released_at" class="flex justify-between items-center">
                                <span class="font-medium text-gray-900 dark:text-white">Sortie</span>
                                <span class="text-gray-600 dark:text-gray-300">{{ new Date(iosVersion.released_at).toLocaleDateString('fr-FR') }}</span>
                            </div>
                        </div>

                        <a 
                            :href="route('download.platform', 'ios')"
                            class="group w-full inline-flex items-center justify-center px-6 py-4 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                        >
                            <span v-html="getStoreIcon('ios')" class="mr-3"></span>
                            <div class="text-left">
                                <div class="text-sm">Télécharger sur</div>
                                <div class="font-semibold">App Store</div>
                            </div>
                        </a>

                        <div v-if="iosVersion.description" class="text-sm text-gray-600 dark:text-gray-400">
                            {{ iosVersion.description }}
                        </div>
                    </div>

                    <div v-else class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400 mb-4">Version iOS non disponible</p>
                        <div class="text-sm text-gray-400 dark:text-gray-500">
                            La version iOS sera bientôt disponible
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8 dark:bg-gray-800/60 dark:border-gray-700/50">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                    Instructions d'installation
                </h3>
                
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- Android Instructions -->
                    <div>
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                            🤖 Android
                        </h4>
                        <ol class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                            <li class="flex items-start">
                                <span class="bg-primary-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">1</span>
                                Cliquez sur le bouton de téléchargement Android
                            </li>
                            <li class="flex items-start">
                                <span class="bg-primary-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">2</span>
                                Autorisez l'installation depuis des sources inconnues si demandé
                            </li>
                            <li class="flex items-start">
                                <span class="bg-primary-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">3</span>
                                Ouvrez le fichier APK téléchargé et suivez les instructions
                            </li>
                        </ol>
                    </div>

                    <!-- iOS Instructions -->
                    <div>
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                            🍎 iOS
                        </h4>
                        <ol class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                            <li class="flex items-start">
                                <span class="bg-primary-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">1</span>
                                Cliquez sur le bouton App Store
                            </li>
                            <li class="flex items-start">
                                <span class="bg-primary-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">2</span>
                                Vous serez redirigé vers l'App Store
                            </li>
                            <li class="flex items-start">
                                <span class="bg-primary-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">3</span>
                                Appuyez sur "Obtenir" pour installer l'application
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
