<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AppVersionTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test de vérification de version avec mise à jour disponible
     */
    public function test_check_version_with_update_available(): void
    {
        // Configuration des variables d'environnement pour le test
        config([
            'app.android_minimum_version' => '1.0.0',
            'app.android_latest_version' => '1.2.0',
            'app.android_store_url' => 'https://play.google.com/store/apps/details?id=com.mbokatour.app'
        ]);

        $response = $this->getJson('/api/app/version?current_version=1.1.0&platform=android');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'minimum_version',
                'latest_version',
                'force_update',
                'has_update',
                'update_message',
                'download_url',
                'features'
            ])
            ->assertJson([
                'minimum_version' => '1.0.0',
                'latest_version' => '1.2.0',
                'force_update' => false,
                'has_update' => true,
            ]);

        $this->assertNotNull($response->json('update_message'));
        $this->assertNotNull($response->json('features'));
    }

    /**
     * Test de vérification de version avec mise à jour forcée
     */
    public function test_check_version_with_force_update(): void
    {
        config([
            'app.android_minimum_version' => '1.1.0',
            'app.android_latest_version' => '1.2.0',
        ]);

        $response = $this->getJson('/api/app/version?current_version=1.0.0&platform=android');

        $response->assertStatus(200)
            ->assertJson([
                'force_update' => true,
                'has_update' => true,
            ]);

        $this->assertStringContainsString('obligatoire', $response->json('update_message'));
    }

    /**
     * Test de vérification de version sans mise à jour nécessaire
     */
    public function test_check_version_no_update_needed(): void
    {
        config([
            'app.android_minimum_version' => '1.0.0',
            'app.android_latest_version' => '1.1.0',
        ]);

        $response = $this->getJson('/api/app/version?current_version=1.1.0&platform=android');

        $response->assertStatus(200)
            ->assertJson([
                'force_update' => false,
                'has_update' => false,
                'update_message' => null,
                'features' => null,
            ]);
    }

    /**
     * Test de vérification de version pour iOS
     */
    public function test_check_version_ios_platform(): void
    {
        config([
            'app.ios_minimum_version' => '1.0.0',
            'app.ios_latest_version' => '1.2.0',
            'app.ios_store_url' => 'https://apps.apple.com/app/mbokatour/id123456789'
        ]);

        $response = $this->getJson('/api/app/version?current_version=1.1.0&platform=ios');

        $response->assertStatus(200)
            ->assertJson([
                'minimum_version' => '1.0.0',
                'latest_version' => '1.2.0',
                'download_url' => 'https://apps.apple.com/app/mbokatour/id123456789'
            ]);
    }

    /**
     * Test de validation des paramètres requis
     */
    public function test_validation_required_parameters(): void
    {
        // Test sans current_version
        $response = $this->getJson('/api/app/version?platform=android');
        $response->assertStatus(422);

        // Test sans platform
        $response = $this->getJson('/api/app/version?current_version=1.0.0');
        $response->assertStatus(422);

        // Test avec format de version invalide
        $response = $this->getJson('/api/app/version?current_version=1.0&platform=android');
        $response->assertStatus(422);

        // Test avec plateforme invalide
        $response = $this->getJson('/api/app/version?current_version=1.0.0&platform=windows');
        $response->assertStatus(422);
    }

    /**
     * Test de la structure de réponse complète
     */
    public function test_response_structure(): void
    {
        $response = $this->getJson('/api/app/version?current_version=1.0.0&platform=android');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'minimum_version',
                'latest_version',
                'force_update',
                'has_update',
                'update_message',
                'download_url',
                'features'
            ]);

        // Vérifier que les types sont corrects
        $data = $response->json();
        $this->assertIsString($data['minimum_version']);
        $this->assertIsString($data['latest_version']);
        $this->assertIsBool($data['force_update']);
        $this->assertIsBool($data['has_update']);
        $this->assertIsString($data['download_url']);
    }
}
